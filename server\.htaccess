# 限制对usage.json和log文件的访问
<Files "usage.json">
    Order Allow,Deny
    Deny from all
</Files>

<Files "api_log.json">
    Order Allow,Deny
    Deny from all
</Files>

<Files "rate_limits.json">
    Order Allow,Deny
    Deny from all
</Files>

# 防止PHP错误泄露信息
php_flag display_errors off

# 限制每分钟请求数 (需要mod_ratelimit模块)
<IfModule mod_ratelimit.c>
    SetOutputFilter RATE_LIMIT
    SetEnv rate-limit 60
</IfModule>

# 防止目录列表
Options -Indexes
ServerSignature Off

# 设置安全响应头
<IfModule mod_headers.c>
    Header set X-Content-Type-Options "nosniff"
    Header set X-Frame-Options "DENY"
    Header set X-XSS-Protection "1; mode=block"
    Header set Referrer-Policy "same-origin"
</IfModule>

# 限制对安全日志的访问
<Files "security_log.json">
    Order Allow,Deny
    Deny from all
</Files>

# 保护敏感JSON文件，但允许访问models.json
<FilesMatch "^((?!models\.json$).)*\.json$">
    Order Deny,Allow
    Deny from all
</FilesMatch>

# 明确允许访问models.json
<Files "models.json">
    Order Allow,Deny
    Allow from all
    <IfModule mod_headers.c>
        Header set Cache-Control "no-store, no-cache, must-revalidate, max-age=0"
        Header set Pragma "no-cache"
        Header set Access-Control-Allow-Origin "*"
        Header set Access-Control-Allow-Methods "GET, POST, OPTIONS"
        Header set Access-Control-Allow-Headers "Content-Type, Authorization"
    </IfModule>
</Files>

# 添加额外的安全措施 - 阻止访问敏感文件
<Files ~ "^(debug_log|config|api_keys)\.json$">
    Order Allow,Deny
    Deny from all
</Files>

# 防止访问所有 .开头的目录和文件
<FilesMatch "^\.">
    Order deny,allow
    Deny from all
</FilesMatch>

# PHP配置
<IfModule mod_php.c>
    php_flag display_errors off
    php_value max_execution_time 300
    php_value memory_limit 256M
</IfModule> 