<?php
// 显示完整错误信息
ini_set('display_errors', 1);
error_reporting(E_ALL);

$scriptDir = __DIR__;
$logDir = $scriptDir . '/logs';

echo "开始修复权限...\n";

// 检查日志目录是否存在
if (!file_exists($logDir)) {
    echo "创建日志目录: $logDir\n";
    if (!mkdir($logDir, 0777, true)) {
        echo "错误: 无法创建目录\n";
        exit(1);
    }
    chmod($logDir, 0777); // 最大权限，确保可写
}

// 获取当前运行脚本的用户
$currentUser = posix_getpwuid(posix_geteuid());
echo "当前用户: {$currentUser['name']}\n";

// 确保日志目录有最大权限
echo "设置日志目录权限: $logDir\n";
chmod($logDir, 0777);
echo "目录权限: " . substr(sprintf('%o', fileperms($logDir)), -4) . "\n";

// 创建测试文件
$testFile = $logDir . '/test_' . time() . '.txt';
echo "创建测试文件: $testFile\n";
if (file_put_contents($testFile, "测试写入权限 " . date('Y-m-d H:i:s') . "\n")) {
    echo "测试文件创建成功\n";
    chmod($testFile, 0666);
    echo "测试文件权限: " . substr(sprintf('%o', fileperms($testFile)), -4) . "\n";
} else {
    echo "错误: 无法写入测试文件\n";
}

// 检查日志文件
$logFiles = ['api.log', 'debug.log', 'security.log', 'error.log'];
foreach ($logFiles as $file) {
    $fullPath = $logDir . '/' . $file;
    if (file_exists($fullPath)) {
        echo "设置日志文件权限: $fullPath\n";
        chmod($fullPath, 0666);
        echo "文件权限: " . substr(sprintf('%o', fileperms($fullPath)), -4) . "\n";
    } else {
        echo "创建空日志文件: $fullPath\n";
        if (file_put_contents($fullPath, "")) {
            chmod($fullPath, 0666);
            echo "文件权限: " . substr(sprintf('%o', fileperms($fullPath)), -4) . "\n";
        } else {
            echo "错误: 无法创建日志文件\n";
        }
    }
}

echo "权限修复完成\n"; 