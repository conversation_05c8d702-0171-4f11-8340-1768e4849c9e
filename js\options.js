/**
 * GLM-Highlight 选项页面脚本
 * 用于管理域名白名单/黑名单的操作
 */

// 域名存储常量
const DOMAIN_RULES_KEY = "domain_rules";
// 添加划词工具栏设置存储键
const TRANSLATOR_TOOLBAR_KEY = "translator_toolbar_enabled";

// 定义数据结构
const defaultRules = {
  mode: "blacklist", // 默认为黑名单模式，更符合用户期望
  domains: [], // 域名数组
};

// DOM 元素引用
const elements = {
  modeRadios: document.querySelectorAll('input[name="mode"]'),
  // 添加划词工具栏单选按钮引用
  translatorRadios: document.querySelectorAll('input[name="translator"]'),
  saveBtn: document.getElementById("saveBtn"),
  exportBtn: document.getElementById("exportBtn"),
  importBtn: document.getElementById("importBtn"),
  importFile: document.getElementById("importFile"),
  addNewDomain: document.getElementById("addNewDomain"),
  domainInputContainer: document.getElementById("domainInputContainer"),
  newDomainInput: document.getElementById("newDomainInput"),
  saveDomainBtn: document.getElementById("saveDomainBtn"),
  cancelDomainBtn: document.getElementById("cancelDomainBtn"),
  domainList: document.getElementById("domainList"),
  emptyState: document.getElementById("emptyState"),
  saveStatus: document.getElementById("saveStatus"),
};

// 保存状态对象
let domainRules = { ...defaultRules };
let isFormDirty = false;
// 添加划词工具栏设置状态
let translatorToolbarEnabled = true;

// 初始化
document.addEventListener("DOMContentLoaded", async () => {
  // 加载保存的域名规则
  await loadDomainRules();

  // 设置界面初始状态
  updateUI();

  // 事件绑定
  setupEventListeners();
});

/**
 * 加载已保存的域名规则
 */
async function loadDomainRules() {
  try {
    const result = await chrome.storage.sync.get([
      DOMAIN_RULES_KEY,
      TRANSLATOR_TOOLBAR_KEY,
    ]);
    if (result[DOMAIN_RULES_KEY]) {
      domainRules = result[DOMAIN_RULES_KEY];
    } else {
      domainRules = { ...defaultRules };
      // 首次加载时保存默认规则
      await chrome.storage.sync.set({ [DOMAIN_RULES_KEY]: domainRules });
    }

    // 加载划词工具栏设置
    if (result[TRANSLATOR_TOOLBAR_KEY] !== undefined) {
      translatorToolbarEnabled = result[TRANSLATOR_TOOLBAR_KEY];
    } else {
      // 首次加载时保存默认值（启用）
      await chrome.storage.sync.set({ [TRANSLATOR_TOOLBAR_KEY]: true });
    }
  } catch (error) {
    console.error("加载配置失败:", error);
    showSaveStatus("加载配置失败", "error");
  }
}

/**
 * 保存域名规则
 */
async function saveDomainRules() {
  try {
    // 保存域名规则
    await chrome.storage.sync.set({ [DOMAIN_RULES_KEY]: domainRules });

    // 保存划词工具栏设置
    await chrome.storage.sync.set({
      [TRANSLATOR_TOOLBAR_KEY]: translatorToolbarEnabled,
    });

    // 通知后台脚本规则已更新
    await chrome.runtime.sendMessage({
      opt: "event",
      event: "domainRulesUpdated",
      data: domainRules,
    });

    // 通知后台脚本划词工具栏设置已更新
    await chrome.runtime.sendMessage({
      opt: "event",
      event: "translatorToolbarUpdated",
      data: translatorToolbarEnabled,
    });

    isFormDirty = false;

    // 提供更清晰的说明，确保用户理解需要刷新标签页
    showSaveStatus(
      "保存成功！重要提示：设置只会在页面刷新后生效，不会立即应用到当前打开的页面。请刷新需要应用规则的标签页查看效果。",
      "success"
    );
  } catch (error) {
    console.error("保存配置失败:", error);
    showSaveStatus("保存失败", "error");
  }
}

/**
 * 更新UI状态
 */
function updateUI() {
  // 设置模式单选框
  Array.from(elements.modeRadios).find(
    (radio) => radio.value === domainRules.mode
  ).checked = true;

  // 设置划词工具栏单选框
  Array.from(elements.translatorRadios).find(
    (radio) =>
      radio.value === (translatorToolbarEnabled ? "enabled" : "disabled")
  ).checked = true;

  // 清空并重绘域名列表
  renderDomainList();
}

/**
 * 渲染域名列表
 */
function renderDomainList() {
  elements.domainList.innerHTML = "";

  // 判断是否显示空状态
  if (domainRules.domains.length === 0) {
    elements.emptyState.style.display = "block";
    return;
  }

  elements.emptyState.style.display = "none";

  // 按字母顺序排序域名
  const sortedDomains = [...domainRules.domains].sort();

  // 创建域名列表
  sortedDomains.forEach((domain) => {
    const row = document.createElement("tr");

    const domainCell = document.createElement("td");
    domainCell.textContent = domain;

    const actionsCell = document.createElement("td");
    const deleteBtn = document.createElement("button");
    deleteBtn.className = "delete-btn";
    deleteBtn.textContent = "删除";
    deleteBtn.onclick = () => removeDomain(domain);

    actionsCell.appendChild(deleteBtn);
    row.appendChild(domainCell);
    row.appendChild(actionsCell);

    elements.domainList.appendChild(row);
  });
}

/**
 * 添加域名
 */
function addDomain(domain) {
  domain = domain.trim();

  // 验证域名
  if (!isValidDomain(domain)) {
    showSaveStatus("请输入有效的域名", "error");
    return false;
  }

  // 检查是否已存在
  if (domainRules.domains.includes(domain)) {
    showSaveStatus("该域名已在列表中", "error");
    return false;
  }

  // 添加域名
  domainRules.domains.push(domain);
  isFormDirty = true;

  // 更新UI
  renderDomainList();
  elements.domainInputContainer.style.display = "none";
  elements.newDomainInput.value = "";

  showSaveStatus("域名已添加，请保存配置", "success");
  return true;
}

/**
 * 删除域名
 */
function removeDomain(domain) {
  domainRules.domains = domainRules.domains.filter((d) => d !== domain);
  isFormDirty = true;

  // 更新UI
  renderDomainList();
  showSaveStatus("域名已删除，请保存配置", "success");
}

/**
 * 显示保存状态消息
 */
function showSaveStatus(message, type = "info") {
  elements.saveStatus.textContent = message;
  elements.saveStatus.className = "save-status";

  if (type) {
    elements.saveStatus.classList.add(`save-${type}`);
  }

  // 3秒后自动清除
  setTimeout(() => {
    elements.saveStatus.textContent = "";
    elements.saveStatus.className = "save-status";
  }, 3000);
}

/**
 * 验证域名格式
 */
function isValidDomain(domain) {
  //允许域名或带路径的URL。这是一个简单的检查。
  //通过在background.js中如何使用规则，将隐式地完成更健壮的验证。
  return domain.includes(".") || domain.includes("/");
}

/**
 * 导出规则为JSON文件
 */
function exportRules() {
  const dataStr = JSON.stringify(domainRules, null, 2);
  const dataUri =
    "data:application/json;charset=utf-8," + encodeURIComponent(dataStr);

  const exportLink = document.createElement("a");
  exportLink.setAttribute("href", dataUri);
  exportLink.setAttribute("download", "glm-highlight-domain-rules.json");
  document.body.appendChild(exportLink);
  exportLink.click();
  document.body.removeChild(exportLink);
}

/**
 * 导入规则从JSON文件
 */
function importRules(event) {
  const file = event.target.files[0];
  if (!file) return;

  const reader = new FileReader();
  reader.onload = function (e) {
    try {
      const importedRules = JSON.parse(e.target.result);

      // 验证导入的数据格式
      if (
        typeof importedRules !== "object" ||
        !["whitelist", "blacklist"].includes(importedRules.mode) ||
        !Array.isArray(importedRules.domains)
      ) {
        throw new Error("无效的配置格式");
      }

      // 更新规则
      domainRules = {
        mode: importedRules.mode,
        domains: importedRules.domains.filter((d) => isValidDomain(d)),
      };

      isFormDirty = true;
      updateUI();
      showSaveStatus("配置已导入，请保存", "success");
    } catch (error) {
      console.error("导入失败:", error);
      showSaveStatus("导入失败: " + error.message, "error");
    }

    // 重置文件输入
    elements.importFile.value = "";
  };

  reader.readAsText(file);
}

/**
 * 设置事件监听
 */
function setupEventListeners() {
  // 模式变更
  elements.modeRadios.forEach((radio) => {
    radio.addEventListener("change", () => {
      // 只更新内存中的数据，不触发保存
      domainRules.mode = radio.value;
      isFormDirty = true;

      // 清晰地提示用户需要点击保存按钮才会生效
      showSaveStatus("模式已更改，请点击「保存配置」按钮使更改生效", "success");

      // 强调保存按钮
      elements.saveBtn.classList.add("highlight-btn");
      setTimeout(() => {
        elements.saveBtn.classList.remove("highlight-btn");
      }, 1500);
    });
  });

  // 添加划词工具栏单选框事件监听
  Array.from(elements.translatorRadios).forEach((radio) => {
    radio.addEventListener("change", () => {
      translatorToolbarEnabled = radio.value === "enabled";
      isFormDirty = true;

      // 清晰地提示用户需要点击保存按钮才会生效
      showSaveStatus(
        "划词工具栏设置已更改，请点击「保存配置」按钮使更改生效",
        "success"
      );

      // 强调保存按钮
      elements.saveBtn.classList.add("highlight-btn");
      setTimeout(() => {
        elements.saveBtn.classList.remove("highlight-btn");
      }, 1500);
    });
  });

  // 保存按钮
  elements.saveBtn.addEventListener("click", () => {
    saveDomainRules();
  });

  // 导出按钮
  elements.exportBtn.addEventListener("click", exportRules);

  // 导入按钮
  elements.importBtn.addEventListener("click", () => {
    elements.importFile.click();
  });

  // 导入文件选择
  elements.importFile.addEventListener("change", importRules);

  // 新增域名按钮
  elements.addNewDomain.addEventListener("click", () => {
    elements.domainInputContainer.style.display = "flex";
    elements.newDomainInput.focus();
  });

  // 保存域名按钮
  elements.saveDomainBtn.addEventListener("click", () => {
    addDomain(elements.newDomainInput.value);
  });

  // 取消添加域名
  elements.cancelDomainBtn.addEventListener("click", () => {
    elements.domainInputContainer.style.display = "none";
    elements.newDomainInput.value = "";
  });

  // 域名输入框回车保存
  elements.newDomainInput.addEventListener("keypress", (e) => {
    if (e.key === "Enter") {
      addDomain(elements.newDomainInput.value);
    }
  });

  // 页面离开警告
  window.addEventListener("beforeunload", (e) => {
    if (isFormDirty) {
      e.preventDefault();
      e.returnValue = "您有未保存的更改，确定要离开吗？";
      return e.returnValue;
    }
  });
}
