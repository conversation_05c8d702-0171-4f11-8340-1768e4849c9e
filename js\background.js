// 统一的缓存管理器
class CacheManager {
  constructor(options = {}) {
    this.cache = new Map();
    this.ttl = options.ttl || 5 * 60 * 1000; // 5分钟
    this.maxAge = options.maxAge || 30 * 60 * 1000; // 30分钟
    this.maxSize = options.maxSize || 1000;
    this.cleanupInterval = options.cleanupInterval || 2 * 60 * 1000;

    // 启动定时清理
    this.cleanupTimer = setInterval(() => this.cleanup(), this.cleanupInterval);
  }

  set(key, value) {
    // 检查缓存大小
    if (this.cache.size >= this.maxSize) {
      const oldestKey = Array.from(this.cache.keys()).sort(
        (a, b) => this.cache.get(a).timestamp - this.cache.get(b).timestamp
      )[0];
      this.cache.delete(oldestKey);
    }

    this.cache.set(key, {
      value,
      timestamp: Date.now(),
      lastAccessed: Date.now(),
    });
  }

  get(key) {
    const item = this.cache.get(key);
    if (!item) return null;

    const now = Date.now();
    if (now - item.timestamp > this.maxAge) {
      this.cache.delete(key);
      return null;
    }

    // 更新访问时间
    item.lastAccessed = now;
    return item.value;
  }

  cleanup() {
    const now = Date.now();
    for (const [key, item] of this.cache) {
      if (
        now - item.timestamp > this.maxAge ||
        now - item.lastAccessed > this.ttl
      ) {
        this.cache.delete(key);
      }
    }
  }

  clear() {
    this.cache.clear();
  }

  destroy() {
    clearInterval(this.cleanupTimer);
    this.clear();
  }
}

const background = {
  USER_ID_STORE: "userUUID",
  ACTIVE_STATUS_STORE: "isActive",
  KEYWORDS_STRING_STORE: "fwm_keywordsString",
  KEYWORDS_ARRAY_STORE: "fwm_keywordsArray",
  TAB_ACTIVE_STATUS: "tabActiveStatus",
  DOMAIN_RULES_KEY: "domain_rules",
  TAB_CATEGORY_STATUS: "tabCategoryStatus",
  TRANSLATOR_TOOLBAR_KEY: "translator_toolbar_enabled",

  // 使用新的缓存管理器
  _cache: new CacheManager({
    ttl: 5 * 60 * 1000,
    maxSize: 1000,
    cleanupInterval: 2 * 60 * 1000,
  }),

  tabActiveStatus: new Map(),
  tabCategoryStatus: new Map(),
  domainRules: null,
  appliedDomainRules: null,

  async setLocalStorage(key, value) {
    try {
      const oldValue = await this.getLocalStorage(key);
      if (oldValue === value) return;

      await chrome.storage.local.set({ [key]: value });
      this._cache.set(key, value);

      await this._broadcastChange(key, value);
    } catch (error) {
      Utils.handleError(error, "setLocalStorage");
    }
  },

  async getLocalStorage(key) {
    try {
      const cached = this._cache.get(key);
      if (cached !== null) return cached;

      const result = await chrome.storage.local.get(key);
      this._cache.set(key, result[key]);
      return result[key];
    } catch (error) {
      Utils.handleError(error, "getLocalStorage");
      return null;
    }
  },

  async _broadcastChange(key, value) {
    try {
      const [activeTab] = await chrome.tabs.query({
        active: true,
        currentWindow: true,
        url: ["http://*/*", "https://*/*"],
      });

      if (activeTab?.id) {
        await chrome.tabs
          .sendMessage(activeTab.id, {
            opt: "event",
            event: "storageChange",
            args: { key, value },
          })
          .catch(() => {});
      }
    } catch (error) {
      Utils.handleError(error, "broadcastChange");
    }
  },

  // 统一的消息验证
  _verifyMessage(message) {
    if (!message || typeof message !== "object") return false;
    if (!message.opt || !message.func) return false;
    if (message.opt !== "rpc") return false;
    if (typeof this[message.func] !== "function") return false;
    return true;
  },

  async setKeywordsString2(keywords, options = {}) {
    try {
      // 处理关键词,确保同一个词只出现一次
      const wordMap = new Map();

      // 提前获取当前存储的关键词列表
      let currentList = null;
      if (options.isTabSpecific) {
        currentList = await this.getLocalStorage(this.KEYWORDS_STRING_STORE);
      }

      const processedKeywords = keywords.reduce((acc, item, idx) => {
        // 获取之前保存的关键词列表，保留原来的status
        let status = item.status;

        // 重要：如果是标签页特定操作，不保存分类开关状态到全局
        if (
          options.isTabSpecific &&
          currentList &&
          Array.isArray(currentList) &&
          currentList[idx]
        ) {
          // 保留原状态，不更新全局分类状态
          status = currentList[idx].status;
        }

        if (item.data) {
          const words = item.data.trim().split(/\s+/).filter(Boolean);
          words.forEach((word) => {
            wordMap.set(word, {
              colour: item.colour,
              words: word,
              categoryIndex: idx,
            });
          });
        }
        return acc;
      }, []);

      // 将 Map 转换为数组,并按照 categoryIndex 排序
      const uniqueKeywords = Array.from(wordMap.values()).sort(
        (a, b) => a.categoryIndex - b.categoryIndex
      );

      // 确保存储的是字符串格式
      const dataToStore =
        typeof keywords === "string" ? keywords : JSON.stringify(keywords);

      // 如果是标签页特定操作，需要保留原有分类开关状态
      if (options.isTabSpecific && currentList && Array.isArray(currentList)) {
        // 创建修改后的列表，但保留原来的status
        const updatedList = keywords.map((item, idx) => {
          if (idx < currentList.length) {
            // 保留原状态，只更新内容和颜色
            return {
              ...item,
              status: currentList[idx].status,
            };
          }
          return item;
        });

        // 保存修改后的数据
        await this.setLocalStorage(this.KEYWORDS_STRING_STORE, updatedList);

        // 警告：不要直接使用uniqueKeywords，因为它已经丢失了分类信息
        // 需要重新处理一遍，确保分类信息正确
        const wordMapWithStatus = new Map();
        updatedList.forEach((item, idx) => {
          if (item.status === 1 && item.data) {
            const words = item.data.trim().split(/\s+/).filter(Boolean);
            words.forEach((word) => {
              wordMapWithStatus.set(word, {
                colour: item.colour,
                words: word,
                categoryIndex: idx,
              });
            });
          }
        });

        // 重新排序
        const filteredKeywords = Array.from(wordMapWithStatus.values()).sort(
          (a, b) => a.categoryIndex - b.categoryIndex
        );

        await this.setKeywords(filteredKeywords);
        return filteredKeywords;
      }

      // 默认行为：正常保存数据
      await this.setLocalStorage(this.KEYWORDS_STRING_STORE, dataToStore);
      await this.setKeywords(uniqueKeywords);

      return uniqueKeywords;
    } catch (error) {
      console.error("设置关键词字符串失败:", error);
      return null;
    }
  },

  async getKeywords() {
    return await this.getLocalStorage(this.KEYWORDS_ARRAY_STORE);
  },

  async setKeywords(keywords) {
    try {
      await this.setLocalStorage(this.KEYWORDS_ARRAY_STORE, keywords);
    } catch (error) {
      console.error("设置关键词失败:", error);
    }
  },

  async setTabActiveStatus(tabId, status) {
    try {
      // 只更新特定标签页状态，不修改全局状态
      this.tabActiveStatus.set(tabId, status);
      // 使用storage.local而不是localStorage，更高效且独立
      await chrome.storage.local.set({
        [`${this.TAB_ACTIVE_STATUS}_${tabId}`]: status,
      });

      // 不再需要调用setLocalStorage，以避免更新全局状态
    } catch (error) {
      console.error("设置标签页状态失败:", error);
    }
  },

  async getTabActiveStatus(tabId) {
    try {
      // 检查域名规则
      const tab = await chrome.tabs.get(tabId).catch(() => null);
      let shouldHighlight = true; // 默认允许高亮

      if (tab && tab.url) {
        shouldHighlight = await this.shouldHighlightDomain(tab.url);
        if (shouldHighlight === false) {
          return false; // 如果域名在黑名单中，禁用高亮
        }
      }

      // 查找标签页特定状态
      if (this.tabActiveStatus.has(tabId)) {
        const status = this.tabActiveStatus.get(tabId);
        // 只有状态明确为false时才返回false，其他情况返回true
        return status !== false;
      }

      // 获取保存的标签页状态
      const status = await this.getLocalStorage(
        `${this.TAB_ACTIVE_STATUS}_${tabId}`
      );

      if (status !== null && status !== undefined) {
        // 只有状态明确为false时才返回false
        const isActive = status !== false;
        this.tabActiveStatus.set(tabId, isActive);
        return isActive;
      }

      // 如果没有找到标签页状态，根据域名规则决定默认状态
      // 这样可以避免用户在某网站关闭高亮后，重新打开时又自动开启
      const defaultStatus = shouldHighlight !== false; // 如果域名允许高亮，默认开启；否则默认关闭
      this.tabActiveStatus.set(tabId, defaultStatus);
      await this.setLocalStorage(
        `${this.TAB_ACTIVE_STATUS}_${tabId}`,
        defaultStatus
      );
      return defaultStatus;
    } catch (error) {
      console.error("获取标签页状态失败:", error);
      return true; // 错误情况下默认开启
    }
  },

  generateUUID() {
    const lut = Array(256)
      .fill()
      .map((_, i) => (i < 16 ? "0" : "") + i.toString(16));
    return () => {
      const d0 = (Math.random() * 0xffffffff) | 0;
      const d1 = (Math.random() * 0xffffffff) | 0;
      const d2 = (Math.random() * 0xffffffff) | 0;
      const d3 = (Math.random() * 0xffffffff) | 0;
      return `${
        lut[d0 & 0xff] +
        lut[(d0 >> 8) & 0xff] +
        lut[(d0 >> 16) & 0xff] +
        lut[(d0 >> 24) & 0xff]
      }-${lut[d1 & 0xff]}${lut[(d1 >> 8) & 0xff]}-${
        lut[((d1 >> 16) & 0x0f) | 0x40]
      }${lut[(d1 >> 24) & 0xff]}-${lut[(d2 & 0x3f) | 0x80]}${
        lut[(d2 >> 8) & 0xff]
      }-${lut[(d2 >> 16) & 0xff]}${lut[(d2 >> 24) & 0xff]}${lut[d3 & 0xff]}${
        lut[(d3 >> 8) & 0xff]
      }${lut[(d3 >> 16) & 0xff]}${lut[(d3 >> 24) & 0xff]}`;
    };
  },

  async getUserId() {
    try {
      let userUUID = await this.getLocalStorage(this.USER_ID_STORE);
      if (!userUUID) {
        userUUID = this.generateUUID();
        await this.setLocalStorage(this.USER_ID_STORE, userUUID);
      }
      return userUUID;
    } catch (error) {
      console.error("获取用户ID失败:", error);
      return null;
    }
  },

  async getActiveStatus() {
    try {
      // 全局状态始终默认为启用
      // 这个函数只在需要获取默认状态时使用，不再控制全局状态
      // 每个标签页的状态由getTabActiveStatus独立管理

      // 检查当前活动标签页 - 但只是为了获取信息，不设置状态
      const [activeTab] = await chrome.tabs
        .query({
          active: true,
          currentWindow: true,
          url: ["http://*/*", "https://*/*"],
        })
        .catch(() => [null]);

      // 如果有活动标签页，则返回其状态，否则返回默认为开启
      if (activeTab?.id) {
        return await this.getTabActiveStatus(activeTab.id);
      }

      return true; // 默认启用
    } catch (error) {
      console.error("获取激活状态失败:", error);
      return true; // 出错默认开启高亮
    }
  },

  async setActiveStatus(status) {
    try {
      // 只获取当前活动标签页
      const [activeTab] = await chrome.tabs.query({
        active: true,
        currentWindow: true,
        url: ["http://*/*", "https://*/*"],
      });

      if (activeTab) {
        // 检查活动标签页是否在黑名单中
        // 注意：使用appliedDomainRules而不是domainRules，确保使用已应用的规则
        const shouldHighlight = await this.shouldHighlightDomain(activeTab.url);

        // 只更新当前标签页状态
        // 重要变更：即使在黑名单中的标签页也允许设置状态
        // 这样用户可以手动关闭/开启高亮，而不受黑名单限制
        const newStatus = shouldHighlight ? status : false;

        // 仅更新标签页状态，不影响全局状态
        this.tabActiveStatus.set(activeTab.id, newStatus);
        await chrome.storage.local.set({
          [`${this.TAB_ACTIVE_STATUS}_${activeTab.id}`]: newStatus,
        });

        // 通知当前标签页更新状态
        try {
          await chrome.tabs.sendMessage(activeTab.id, {
            opt: "event",
            event: "updateHighlightStatus",
            shouldHighlight: shouldHighlight,
            tabStatus: newStatus,
          });
        } catch (err) {
          // 忽略不可发送消息的错误
          console.log("无法发送消息到标签页", activeTab.id);
        }
      }
    } catch (error) {
      console.error("设置激活状态失败:", error);
    }
  },

  async getKeywordsString() {
    try {
      return (await this.getLocalStorage(this.KEYWORDS_STRING_STORE)) || "";
    } catch (error) {
      console.error("获取关键词字符串失败:", error);
      return "";
    }
  },

  async getKeywordsString2() {
    try {
      const data = await this.getLocalStorage(this.KEYWORDS_STRING_STORE);
      if (!data) return [];

      // 如果已经是对象就直接返回,否则尝试解析
      return typeof data === "object" ? data : JSON.parse(data);
    } catch (error) {
      console.error("获取关键词字符串2失败:", error);
      return [];
    }
  },

  async reapplyHighlights() {
    try {
      const [activeTab] = await chrome.tabs.query({
        active: true,
        currentWindow: true,
        url: ["http://*/*", "https://*/*"],
      });

      if (activeTab) {
        await chrome.tabs
          .sendMessage(activeTab.id, {
            opt: "event",
            event: "reapplyHighlights",
          })
          .catch(() => {});
      }
    } catch (error) {
      console.warn("Reapply highlights failed:", error);
    }
  },

  // 优化消息处理的批处理
  async processBatchMessages(messages) {
    const batchSize = this.config.performance.calculateBatchSize();
    for (let i = 0; i < messages.length; i += batchSize) {
      const batch = messages.slice(i, i + batchSize);
      await Promise.all(batch.map((msg) => this.processMessage(msg)));

      // 使用配置的延迟
      await new Promise((resolve) =>
        setTimeout(resolve, this.config.performance.processDelay)
      );
    }
  },

  async getDomainRules() {
    if (this.domainRules) {
      return this.domainRules;
    }

    try {
      const result = await chrome.storage.sync.get(this.DOMAIN_RULES_KEY);
      const rules = result[this.DOMAIN_RULES_KEY];
      if (rules) {
        this.domainRules = rules;
        // 同时初始化应用规则缓存
        this.appliedDomainRules = JSON.parse(JSON.stringify(rules));
      } else {
        // 修改默认规则 - 黑名单模式且默认为空，意味着默认情况下所有网站都允许高亮
        this.domainRules = {
          mode: "blacklist",
          domains: [],
        };
        // 初始化应用规则缓存
        this.appliedDomainRules = JSON.parse(JSON.stringify(this.domainRules));
        // 保存默认规则到存储
        await chrome.storage.sync.set({
          [this.DOMAIN_RULES_KEY]: this.domainRules,
        });
      }
      return this.domainRules;
    } catch (error) {
      console.error("获取域名规则失败:", error);
      return {
        mode: "blacklist",
        domains: [],
      };
    }
  },

  async shouldHighlightDomain(url) {
    try {
      // 如果没有URL，默认高亮
      if (!url) return true;

      // 解析域名
      let domain;
      try {
        domain = new URL(url).hostname;
      } catch (e) {
        console.error("URL解析错误:", e);
        return true; // URL解析失败默认允许高亮
      }

      if (!domain) return true;

      // 重要修改：使用已应用的规则缓存，而不是最新规则
      // 如果应用规则缓存不存在，初始化它
      if (!this.appliedDomainRules) {
        // 获取最新规则并用它初始化应用规则缓存
        await this.getDomainRules();
        this.appliedDomainRules = JSON.parse(JSON.stringify(this.domainRules));
      }

      // 使用应用规则缓存
      const rules = this.appliedDomainRules;

      // 检查域名/URL是否在列表中
      const isDomainInList = rules.domains.some((d) => {
        // 如果规则包含'/'，则视为URL规则
        if (d.includes("/")) {
          // 简单检查：当前URL是否包含规则字符串
          return url.includes(d);
        }
        // 否则，视为域名规则
        // 精确匹配
        if (domain === d) return true;
        // 子域名匹配 (如果 d 是 example.com，domain 是 sub.example.com 也匹配)
        if (domain.endsWith("." + d)) return true;
        return false;
      });

      // 根据模式判断
      if (rules.mode === "whitelist") {
        // 白名单模式：域名在列表中才高亮
        return isDomainInList;
      } else {
        // 黑名单模式：域名不在列表中才高亮
        return !isDomainInList;
      }
    } catch (error) {
      console.error("域名检查失败:", error);
      return true; // 出错默认高亮
    }
  },

  async handleDomainRulesUpdate(rules) {
    try {
      // 只更新内存中的规则，但不修改应用的缓存规则
      this.domainRules = rules;

      // 记录规则已更改，提示用户需要刷新页面才会生效
      console.log("域名规则已更新，但只有在页面刷新后才会生效");

      // 不自动更新应用的缓存规则，确保不会立即生效
      // 规则只会在标签页刷新/重新加载时更新（在onUpdated事件处理中）

      // 存储最新的规则到存储，但不应用
      await chrome.storage.sync.set({
        [this.DOMAIN_RULES_KEY]: rules,
      });
    } catch (error) {
      console.error("处理域名规则更新失败:", error);
    }
  },

  async updateTabHighlightStatus(tabId, url) {
    try {
      // 使用已应用的缓存规则检查域名，而不是最新规则
      // 确保黑/白名单设置只在刷新后生效
      const shouldHighlight = await this.shouldHighlightDomain(url);

      // 获取标签页当前状态 - 优先使用已保存的状态
      let tabStatus = null;
      if (this.tabActiveStatus.has(tabId)) {
        tabStatus = this.tabActiveStatus.get(tabId);
      } else {
        // 如果没有找到标签页状态，从存储中获取
        tabStatus = await chrome.storage.local
          .get(`${this.TAB_ACTIVE_STATUS}_${tabId}`)
          .then((result) => result[`${this.TAB_ACTIVE_STATUS}_${tabId}`]);
      }

      // 重要：如果没有特定状态，获取默认的全局状态
      const globalActive =
        tabStatus !== null ? tabStatus : await this.getActiveStatus();

      // 修改核心逻辑：根据域名规则和当前标签页状态决定是否高亮
      // 关键改进：只有当标签页没有特定状态时，才使用域名规则
      let finalStatus;
      if (tabStatus !== null && tabStatus !== undefined) {
        // 如果标签页已有特定状态，优先使用该状态
        // 但仍然受域名规则影响，除非用户手动设置过
        finalStatus = shouldHighlight ? tabStatus : false;
      } else {
        // 如果标签页没有特定状态，根据域名规则和全局状态决定
        finalStatus = shouldHighlight ? globalActive : false;
      }

      // 更新内存和存储中的标签页状态
      this.tabActiveStatus.set(tabId, finalStatus);
      await chrome.storage.local.set({
        [`${this.TAB_ACTIVE_STATUS}_${tabId}`]: finalStatus,
      });

      // 通知内容脚本更新高亮状态，提供域名规则和标签页状态
      try {
        await chrome.tabs.sendMessage(tabId, {
          opt: "event",
          event: "updateHighlightStatus",
          shouldHighlight: shouldHighlight,
          tabStatus: finalStatus,
        });
      } catch (err) {
        // 忽略不可发送消息的错误（例如未加载的页面）
        console.log("无法发送消息到标签页", tabId, err);
      }
    } catch (error) {
      console.error("更新标签页高亮状态失败:", error);
    }
  },

  // 新增：广播消息到所有活动标签页
  // 此函数已移除以避免不必要的同步

  // 添加：更新应用的规则缓存（在页面刷新时调用）
  async updateAppliedRules() {
    try {
      // 确保domainRules已经是最新的
      if (!this.domainRules) {
        await this.getDomainRules();
      }

      // 将最新编辑的规则复制到应用规则缓存
      // 使用深拷贝确保两个对象完全独立
      this.appliedDomainRules = JSON.parse(JSON.stringify(this.domainRules));
      console.log("已更新应用的域名规则缓存", {
        mode: this.appliedDomainRules.mode,
        domainsCount: this.appliedDomainRules.domains.length,
      });

      return this.appliedDomainRules;
    } catch (error) {
      console.error("更新应用规则缓存失败:", error);
      // 如果更新失败，确保appliedDomainRules至少有一个有效值
      if (!this.appliedDomainRules) {
        this.appliedDomainRules = {
          mode: "blacklist",
          domains: [],
        };
      }
      return this.appliedDomainRules;
    }
  },

  // 添加获取标签页分类状态的方法
  async getTabCategoryStatus(tabId, categoryIndex) {
    try {
      // 如果存在缓存，优先使用缓存
      const cacheKey = `${tabId}_${categoryIndex}`;
      if (this.tabCategoryStatus.has(cacheKey)) {
        return this.tabCategoryStatus.get(cacheKey);
      }

      // 否则从存储中获取
      const storageKey = `${this.TAB_CATEGORY_STATUS}_${tabId}_${categoryIndex}`;
      const result = await chrome.storage.local.get(storageKey);
      const status = result[storageKey];

      // 如果存储中不存在，返回 null（表示使用全局状态）
      if (status === undefined) {
        return null;
      }

      // 更新缓存
      this.tabCategoryStatus.set(cacheKey, status);
      return status;
    } catch (error) {
      console.error("获取标签页分类状态失败:", error);
      return null; // 出错时返回 null（使用全局状态）
    }
  },

  // 添加设置标签页分类状态的方法
  async setTabCategoryStatus(tabId, categoryIndex, status) {
    try {
      // 更新缓存
      const cacheKey = `${tabId}_${categoryIndex}`;
      this.tabCategoryStatus.set(cacheKey, status);

      // 更新存储
      const storageKey = `${this.TAB_CATEGORY_STATUS}_${tabId}_${categoryIndex}`;
      await chrome.storage.local.set({ [storageKey]: status });

      // 记录日志
      console.log(
        `设置标签页 ${tabId} 的分类 ${categoryIndex} 状态为: ${
          status ? "启用" : "禁用"
        }`
      );

      return true;
    } catch (error) {
      console.error("设置标签页分类状态失败:", error);
      return false;
    }
  },

  // 添加获取当前标签页ID的方法
  async getCurrentTabId() {
    try {
      const [tab] = await chrome.tabs.query({
        active: true,
        currentWindow: true,
      });
      return tab?.id;
    } catch (error) {
      console.error("获取当前标签页ID失败:", error);
      return null;
    }
  },
};

// 消息监听
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  try {
    // 处理事件消息
    if (message.opt === "event") {
      switch (message.event) {
        case "domainRulesUpdated":
          background.handleDomainRulesUpdate(message.data);
          sendResponse({ success: true });
          break;
        case "translatorToolbarUpdated":
          // 处理划词工具栏设置更新
          console.log("划词工具栏设置已更新:", message.data);
          // 广播设置变更到所有标签页
          chrome.tabs.query({}, (tabs) => {
            tabs.forEach((tab) => {
              if (tab.url && tab.url.startsWith("http")) {
                chrome.tabs
                  .sendMessage(tab.id, {
                    opt: "event",
                    event: "translatorToolbarSettingChanged",
                    data: message.data,
                  })
                  .catch(() => {
                    // 忽略发送失败的错误
                  });
              }
            });
          });
          sendResponse({ success: true });
          break;
        default:
          console.warn("未处理的事件类型:", message.event);
          sendResponse({ success: false, error: "未处理的事件类型" });
      }
      return true;
    }

    // 处理 RPC 消息
    if (message.opt === "rpc") {
      // 验证消息格式
      if (!background._verifyMessage(message)) {
        sendResponse({ success: false, error: "无效的RPC消息格式" });
        return true;
      }

      // 批量处理多个函数调用
      if (message.func === "batch") {
        background.processBatchMessages(message.args).then(sendResponse);
        return true;
      }

      // 调用对应的函数
      const func = background[message.func];
      if (typeof func === "function") {
        func.apply(background, message.args || []).then(sendResponse);
        return true;
      } else {
        sendResponse({ success: false, error: "函数不存在" });
      }
      return true;
    }
  } catch (error) {
    console.error("处理消息时出错:", error);
    sendResponse({ success: false, error: error.message });
  }
  return true;
});

// 标签页事件处理
chrome.tabs.onRemoved.addListener((tabId) => {
  // 使用延迟清理，避免与其他事件的竞态条件
  setTimeout(async () => {
    try {
      // 清理总开关状态
      background.tabActiveStatus.delete(tabId);
      await chrome.storage.local.remove(
        `${background.TAB_ACTIVE_STATUS}_${tabId}`
      );

      // 清理分类开关状态
      const keysToRemove = [];

      // 清理内存中的键
      background.tabCategoryStatus.forEach((value, key) => {
        if (key.startsWith(`${tabId}_`)) {
          background.tabCategoryStatus.delete(key);
        }
      });

      // 获取存储中以该标签页ID开头的所有键
      const categoryKeyPrefix = `${background.TAB_CATEGORY_STATUS}_${tabId}_`;

      // 使用Promise版本的storage API
      const items = await chrome.storage.local.get(null);
      Object.keys(items).forEach((key) => {
        if (key.startsWith(categoryKeyPrefix)) {
          keysToRemove.push(key);
        }
      });

      // 一次性批量删除存储
      if (keysToRemove.length > 0) {
        await chrome.storage.local.remove(keysToRemove);
        console.log(
          `已清理标签页 ${tabId} 的 ${keysToRemove.length} 个分类状态`
        );
      }
    } catch (error) {
      console.error(`清理标签页 ${tabId} 状态失败:`, error);
    }
  }, 100); // 延迟100ms执行清理，确保其他事件处理完成
});

// 注册右键菜单和初始化插件
chrome.runtime.onInstalled.addListener(async () => {
  try {
    // 先获取当前状态,使用正确的键名
    const currentStatus = await chrome.storage.local.get([
      background.ACTIVE_STATUS_STORE,
    ]);

    // 只设置插件状态为启用,不创建默认分类
    if (!currentStatus[background.ACTIVE_STATUS_STORE]) {
      await chrome.storage.local.set({
        [background.ACTIVE_STATUS_STORE]: "true", // 使用字符串"true"保持一致性
      });
    }

    // 创建右键菜单
    chrome.contextMenus.create({
      id: "add-to-category",
      title: "添加到高亮分类",
      contexts: ["selection"],
    });

    chrome.contextMenus.create({
      id: "remove-highlight",
      title: "删除高亮",
      contexts: ["selection"],
    });
  } catch (error) {
    console.error("初始化插件失败:", error);
  }
});

// 处理右键菜单点击
chrome.contextMenus.onClicked.addListener((info, tab) => {
  if (info.menuItemId === "add-to-category") {
    chrome.tabs.sendMessage(tab.id, {
      type: "add-to-category",
    });
  } else if (info.menuItemId === "remove-highlight") {
    chrome.tabs.sendMessage(tab.id, {
      type: "remove-highlight",
    });
  }
});

// 处理快捷键
chrome.commands.onCommand.addListener((command, tab) => {
  if (command === "add-to-category") {
    chrome.tabs.sendMessage(tab.id, {
      type: "add-to-category",
    });
  } else if (command === "remove-highlight") {
    chrome.tabs.sendMessage(tab.id, {
      type: "remove-highlight",
    });
  }
});

// 检查是否启用文本工具
chrome.tabs.onUpdated.addListener(function (tabId, changeInfo, tab) {
  if (changeInfo.status === "complete" && tab.url && tab.url.match(/^http/)) {
    // 检查是否启用划词工具栏
    chrome.storage.sync.get(
      background.TRANSLATOR_TOOLBAR_KEY,
      function (result) {
        // 只有当设置为true或未设置（默认为true）时，才注入脚本
        if (result[background.TRANSLATOR_TOOLBAR_KEY] !== false) {
          chrome.scripting.executeScript({
            target: { tabId: tabId },
            files: ["js/translator.js"],
          });
        }
      }
    );
  }
});

async function setupContextMenu() {
  try {
    // 移除所有现有菜单
    await chrome.contextMenus.removeAll();

    // 添加选项页面菜单
    chrome.contextMenus.create({
      id: "options",
      title: "网站高亮设置",
      contexts: ["action"],
    });
  } catch (error) {
    console.error("设置上下文菜单失败:", error);
  }
}

// 添加 - 监听tab更新事件
chrome.tabs.onUpdated.addListener(async (tabId, changeInfo, tab) => {
  if (changeInfo.status === "complete" && tab.url) {
    // 重要：在标签页完成加载时更新应用的规则缓存
    await background.updateAppliedRules();

    // 检查并更新高亮状态
    await background.updateTabHighlightStatus(tabId, tab.url);
  }
});

// 添加 - 监听tab激活事件
chrome.tabs.onActivated.addListener(async (activeInfo) => {
  try {
    // 添加延迟和重试机制，避免竞态条件
    const getTabWithRetry = async (tabId, retries = 3) => {
      for (let i = 0; i < retries; i++) {
        try {
          const tab = await chrome.tabs.get(tabId);
          return tab;
        } catch (error) {
          if (error.message.includes("No tab with id") && i < retries - 1) {
            // 标签页可能正在关闭或切换，等待一下再重试
            await new Promise((resolve) => setTimeout(resolve, 50));
            continue;
          }
          throw error;
        }
      }
    };

    const tab = await getTabWithRetry(activeInfo.tabId);
    if (tab && tab.url) {
      // 注意：标签激活时不更新规则缓存，只在页面刷新时更新
      // 检查并更新高亮状态
      await background.updateTabHighlightStatus(tab.id, tab.url);
    }
  } catch (error) {
    // 如果是标签页不存在的错误，静默处理（标签页可能已关闭）
    if (error.message.includes("No tab with id")) {
      console.log(`标签页 ${activeInfo.tabId} 不存在，可能已关闭`);
    } else {
      console.error("标签激活处理失败:", error);
    }
  }
});

// 添加 - 处理右键菜单点击
chrome.contextMenus.onClicked.addListener((info, tab) => {
  if (info.menuItemId === "options") {
    chrome.runtime.openOptionsPage();
  }
});

// 添加 - 初始化扩展
async function initializeExtension() {
  // 设置右键菜单
  await setupContextMenu();

  // 加载域名规则
  await background.getDomainRules();
}

// 扩展启动时初始化
chrome.runtime.onInstalled.addListener(initializeExtension);
