# GLM-Highlight-3.3.4

GLM-Highlight 是一个浏览器扩展程序，提供强大的文本高亮功能，支持多分类高亮、颜色自定义、配置导入导出、分享码功能，让您的阅读体验更加丝滑。

## 项目结构

```
D:.
├─ dialog.html          # 对话框界面
├─ help.html           # 帮助页面
├─ manage.html         # 管理页面
├─ manifest.json       # 扩展程序配置文件
├─ popup.html          # 弹出窗口界面
├─ options.html        # 选项页面
│
├─.vscode             # VS Code 配置目录
│  └─ settings.json   # VS Code 设置
│
├─css                 # 样式文件目录
│  ├─ element-plus.css # Element Plus UI 框架样式
│  └─ highlight.css    # 高亮功能样式
│
├─img                 # 图片资源目录
│  ├─ delete.png      # 删除图标
│  ├─ icon128.png     # 128x128 图标
│  ├─ icon16.png      # 16x16 图标
│  ├─ icon48.png      # 48x48 图标
│  └─ logo.png        # 项目 logo
│
├─js                  # JavaScript 源码目录
│  ├─ background.js   # 后台脚本
│  ├─ config.js       # 配置文件
│  ├─ content-action.js # 内容操作脚本
│  ├─ dialog.js       # 对话框功能
│  ├─ highlighter.js  # 高亮功能核心
│  ├─ jquery.js       # jQuery 库
│  ├─ manage.js       # 管理功能
│  ├─ options.js      # 选项设置
│  ├─ popup.js        # 弹出窗口功能
│  ├─ translator.js   # 翻译功能
│  └─ utils.js        # 工具函数
│
└─server              # 服务器端文件
   ├─ .htaccess       # Apache 配置
   ├─ .php            # PHP 脚本
   ├─ api.geluman.cn.conf # API 配置
   ├─ api_keys.json   # API 密钥配置
   ├─ emergency_cleanup.php # 紧急清理脚本
   ├─ fix_permissions.php  # 权限修复脚本
   ├─ models.json     # 模型配置
   └─ proxy.php       # 代理服务脚本

```

## 主要功能

1. 文本高亮
   - 支持多种颜色的文本高亮
   - 高亮内容管理与分类
   - 高亮样式自定义
   - 高亮关键词导入导出
   - 分享码功能

2. 性能优化
   - 高效的DOM变化处理
   - 优化的关键词匹配算法
   - 时间分片处理大型文档
   - 视口优先渲染策略

3. 高级功能
   - 域名黑白名单
   - 分类独立开关
   - 快捷键支持
   - 右键菜单集成

4. 用户界面
   - 弹出式操作面板
   - 高亮管理界面
   - 设置配置界面
   - 帮助文档

## 技术栈

- 前端：HTML, CSS, JavaScript, jQuery
- 浏览器扩展：Chrome Extension API (Manifest V3)

## 安装说明

1. 下载本项目代码
2. 打开 Chrome 浏览器，进入扩展程序页面
3. 开启开发者模式
4. 点击"加载已解压的扩展程序"
5. 选择项目目录即可完成安装

## 开发说明

本项目使用 Chrome 扩展开发模式 (Manifest V3)，开发时请遵循以下规范：

1. 代码规范
   - 使用 ES6+ 语法
   - 保持代码注释完整
   - 遵循模块化开发原则

2. 性能优化原则
   - 使用时间分片处理大量DOM
   - 优先处理视口内容
   - 避免长时间阻塞主线程
   - 高效的关键词匹配算法

## 性能优化亮点

1. DOM 变化处理优化
   - 实现时间分片处理机制，避免长时间阻塞主线程
   - 视口内容优先处理策略，提升用户体验
   - 高效的DOM观察器，只监控必要的DOM变化
   - 智能的节点过滤和缓存机制

2. 高亮匹配算法优化
   - 优化的匹配查找逻辑，提高查找效率
   - 高效的范围管理，使用二分插入替代频繁排序
   - 超长文本特殊处理，避免处理超大文本而导致的卡顿
   - 批量DOM更新，减少重绘次数

## 版本历史

### 当前版本：3.3.4
- 优化高亮匹配算法，显著提升性能
- 添加超长文本处理逻辑，避免大文本处理导致的卡顿
- 优化范围管理，使用二分查找和插入替代频繁排序
- 错误处理增强，提高扩展稳定性

### 版本 3.3.3
- 优化DOM变化处理流程，实现时间分片处理
- 添加视口优先处理策略，提升用户体验
- 优化观察器逻辑，减少不必要的处理
- 添加资源管理机制，防止内存泄漏

### 版本 3.1.7
- 添加域名黑白名单功能
- 优化分类管理功能
- 修复多个已知问题

## 许可证

[请补充许可证信息]

## 联系方式

[请补充联系方式]
