<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>GLM-Highlight 设置</title>
    <link rel="stylesheet" href="css/element-plus.css" />
    <link rel="stylesheet" href="css/highlight.css" />
    <link rel="stylesheet" href="css/options.css" />
    <script src="js/jquery.js"></script>
    <script src="js/utils.js"></script>
  </head>
  <body>
    <div class="container">
      <header class="header">
        <h1>高亮黑白名单网站设置</h1>
        <p class="subtitle">配置允许或禁止高亮的网站域名</p>
      </header>

      <div class="main-content">
        <!-- 左侧面板 -->
        <div class="left-panel">
          <div class="card">
            <h2>高亮控制模式</h2>
            <div class="mode-selector">
              <label class="radio-container">
                <input type="radio" name="mode" value="blacklist" checked />
                <span class="radio-label"
                  >黑名单模式（默认所有网站都高亮，除了列表中的网站）</span
                >
              </label>
              <label class="radio-container">
                <input type="radio" name="mode" value="whitelist" />
                <span class="radio-label"
                  >白名单模式（默认所有网站都不高亮，只有列表中的网站高亮）</span
                >
              </label>
            </div>

            <!-- 添加划词工具栏设置区域 -->
            <h2>划词工具栏设置</h2>
            <div class="mode-selector">
              <label class="radio-container">
                <input type="radio" name="translator" value="enabled" checked />
                <span class="radio-label"
                  >启用划词工具栏（选中文本后显示翻译工具）</span
                >
              </label>
              <label class="radio-container">
                <input type="radio" name="translator" value="disabled" />
                <span class="radio-label"
                  >禁用划词工具栏（不显示翻译工具）</span
                >
              </label>
            </div>

            <div class="buttons-group">
              <button id="exportBtn" class="btn btn-secondary">
                <span class="icon">↓</span> 导出配置
              </button>
              <button id="importBtn" class="btn btn-secondary">
                <span class="icon">↑</span> 导入配置
              </button>
              <input
                type="file"
                id="importFile"
                accept=".json"
                style="display: none"
              />
            </div>

            <div class="save-section">
              <button id="saveBtn" class="btn btn-primary">保存配置</button>
              <div id="saveStatus" class="save-status"></div>
            </div>
          </div>
        </div>

        <!-- 右侧面板 -->
        <div class="right-panel">
          <div class="card">
            <div class="domain-header">
              <h2>域名列表</h2>
              <div class="actions">
                <button id="addNewDomain" class="btn btn-primary">
                  添加域名
                </button>
              </div>
            </div>

            <div
              class="domain-input-container"
              id="domainInputContainer"
              style="display: none"
            >
              <input
                type="text"
                id="newDomainInput"
                placeholder="输入域名 (例如: example.com, blog.example.com)"
                class="domain-input"
              />
              <div class="input-buttons">
                <button id="saveDomainBtn" class="btn btn-small">添加</button>
                <button
                  id="cancelDomainBtn"
                  class="btn btn-small btn-secondary"
                >
                  取消
                </button>
              </div>
            </div>

            <div class="domain-list-container">
              <table class="domain-table">
                <thead>
                  <tr>
                    <th>域名</th>
                    <th>操作</th>
                  </tr>
                </thead>
                <tbody id="domainList">
                  <!-- 域名列表将通过JS动态填充 -->
                </tbody>
              </table>
            </div>

            <div class="empty-state" id="emptyState">
              <p>当前列表为空，点击"添加域名"按钮添加</p>
            </div>
          </div>
        </div>
      </div>

      <footer class="footer">
        <p>GLM-Highlight &copy; 2025 - 高效的网页文本高亮工具</p>
      </footer>
    </div>

    <script src="js/options.js"></script>
  </body>
</html>
