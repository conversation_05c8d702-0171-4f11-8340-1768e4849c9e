<?php
// 完全独立的日志清理脚本
ini_set('display_errors', 1);
error_reporting(E_ALL);

// 使用绝对路径
$scriptDir = __DIR__;
$logDir = $scriptDir . '/logs';

// 记录开始执行
$logFile = $scriptDir . '/cleanup_emergency.log';
file_put_contents($logFile, date('Y-m-d H:i:s') . " - 开始执行独立清理脚本\n", FILE_APPEND);

try {
    // 1. 确保日志目录存在
    if (!file_exists($logDir)) {
        mkdir($logDir, 0755, true);
        file_put_contents($logFile, date('Y-m-d H:i:s') . " - 创建日志目录: $logDir\n", FILE_APPEND);
    }

    // 2. 定义要清理的日志文件
    $logFiles = [
        'api' => $logDir . '/api.log',
        'debug' => $logDir . '/debug.log',
        'security' => $logDir . '/security.log',
        'error' => $logDir . '/error.log'
    ];

    // 3. 记录当前文件大小
    $fileSizes = [];
    foreach ($logFiles as $type => $file) {
        if (file_exists($file)) {
            $fileSizes[$type] = filesize($file);
            file_put_contents($logFile, date('Y-m-d H:i:s') . " - 文件 $file 大小: " . round($fileSizes[$type]/1024/1024, 2) . "MB\n", FILE_APPEND);
        } else {
            $fileSizes[$type] = 0;
            file_put_contents($logFile, date('Y-m-d H:i:s') . " - 文件 $file 不存在\n", FILE_APPEND);
        }
    }

    // 4. 处理每个日志文件
    foreach ($logFiles as $type => $file) {
        if (file_exists($file) && filesize($file) > 3 * 1024 * 1024) {
            file_put_contents($logFile, date('Y-m-d H:i:s') . " - 文件 $file 需要轮转\n", FILE_APPEND);
            
            // 创建备份文件名
            $backupFile = $file . '.bak.' . date('Ymd_His');
            
            // 尝试重命名文件
            if (rename($file, $backupFile)) {
                file_put_contents($logFile, date('Y-m-d H:i:s') . " - 成功轮转: $file -> $backupFile\n", FILE_APPEND);
                
                // 创建新的空日志文件
                if (touch($file)) {
                    chmod($file, 0666); // 确保新文件可写
                    file_put_contents($logFile, date('Y-m-d H:i:s') . " - 创建新空文件: $file\n", FILE_APPEND);
                } else {
                    file_put_contents($logFile, date('Y-m-d H:i:s') . " - 无法创建新文件: $file\n", FILE_APPEND);
                }
            } else {
                file_put_contents($logFile, date('Y-m-d H:i:s') . " - 轮转失败: $file\n", FILE_APPEND);
                
                // 尝试获取错误信息
                $error = error_get_last();
                file_put_contents($logFile, date('Y-m-d H:i:s') . " - 错误信息: " . ($error ? json_encode($error) : '未知') . "\n", FILE_APPEND);
                
                // 检查文件权限
                $perms = fileperms($file);
                $owner = fileowner($file);
                $group = filegroup($file);
                file_put_contents($logFile, date('Y-m-d H:i:s') . " - 文件权限: " . substr(sprintf('%o', $perms), -4) . ", 所有者: $owner, 组: $group\n", FILE_APPEND);
                
                // 尝试直接清空文件内容
                file_put_contents($file, '');
                file_put_contents($logFile, date('Y-m-d H:i:s') . " - 尝试清空文件内容\n", FILE_APPEND);
            }
        } else {
            file_put_contents($logFile, date('Y-m-d H:i:s') . " - 文件 $file " . (file_exists($file) ? "大小正常，不需要轮转" : "不存在") . "\n", FILE_APPEND);
        }
    }
    
    // 5. 检查rate_limits.json文件
    $rateLimitFile = $scriptDir . '/rate_limits.json';
    $logsRateLimitFile = $logDir . '/rate_limits.json';
    
    if (file_exists($rateLimitFile)) {
        file_put_contents($logFile, date('Y-m-d H:i:s') . " - 发现根目录下的rate_limits.json文件\n", FILE_APPEND);
        
        if (rename($rateLimitFile, $logsRateLimitFile)) {
            file_put_contents($logFile, date('Y-m-d H:i:s') . " - 已移动rate_limits.json到logs目录\n", FILE_APPEND);
            chmod($logsRateLimitFile, 0666);
        } else {
            file_put_contents($logFile, date('Y-m-d H:i:s') . " - 移动rate_limits.json失败\n", FILE_APPEND);
        }
    }
    
    // 6. 记录最终结果
    file_put_contents($logFile, date('Y-m-d H:i:s') . " - 清理操作完成\n", FILE_APPEND);
    
    // 7. 记录清理后的文件大小
    foreach ($logFiles as $type => $file) {
        if (file_exists($file)) {
            $newSize = filesize($file);
            file_put_contents($logFile, date('Y-m-d H:i:s') . " - 文件 $file 清理后大小: " . round($newSize/1024/1024, 2) . "MB\n", FILE_APPEND);
        }
    }

} catch (Exception $e) {
    file_put_contents($logFile, date('Y-m-d H:i:s') . " - 异常: " . $e->getMessage() . "\n", FILE_APPEND);
    file_put_contents($logFile, date('Y-m-d H:i:s') . " - 堆栈: " . $e->getTraceAsString() . "\n", FILE_APPEND);
}

// 输出成功消息
echo "清理脚本已执行完毕。详细日志见: $logFile\n"; 