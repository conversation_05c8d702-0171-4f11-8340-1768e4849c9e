# 定义详细的日志格式
log_format detailed '$remote_addr - $remote_user [$time_local] "$request" '
                    '$status $body_bytes_sent "$http_referer" '
                    '"$http_user_agent" "$http_x_forwarded_for" '
                    '"$request_body" "$http_origin"';

server {
    listen 80;
    listen 443 ssl http2;
    server_name api.geluman.cn;
    index index.php;
    root /www/wwwroot/api.geluman.cn;

    # SSL配置
    ssl_certificate    /www/server/panel/vhost/cert/api.geluman.cn/fullchain.pem;
    ssl_certificate_key    /www/server/panel/vhost/cert/api.geluman.cn/privkey.pem;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers EECDH+CHACHA20:EECDH+AES128:EECDH+AES256:!MD5:!RC4;
    ssl_prefer_server_ciphers on;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;
    
    # 强制HTTPS
    if ($scheme != "https") {
        return 301 https://$server_name$request_uri;
    }

    # proxy.php 的专门配置
    location = /proxy.php {
        # 禁用缓存
        fastcgi_cache off;
        fastcgi_buffering off;
        proxy_buffering off;
        
        # 禁用压缩
        gzip off;
        
        # 添加缓存控制头
        add_header Cache-Control "no-store, no-cache, must-revalidate, max-age=0" always;
        add_header Pragma "no-cache" always;
        add_header Expires "Mon, 26 Jul 1997 05:00:00 GMT" always;
        add_header X-Accel-Buffering "no" always;
        
        # 快速处理OPTIONS请求
        if ($request_method = 'OPTIONS') {
            add_header 'Access-Control-Allow-Origin' '*';
            add_header 'Access-Control-Allow-Methods' 'GET, POST, OPTIONS';
            add_header 'Access-Control-Allow-Headers' 'Content-Type, Authorization, Cache-Control, Pragma, Expires, Accept';
            add_header 'Access-Control-Max-Age' '1728000';
            add_header 'Content-Type' 'text/plain charset=UTF-8';
            add_header 'Content-Length' '0';
            return 204;
        }
        
        # FastCGI 配置
        fastcgi_pass unix:/tmp/php-cgi-80.sock;
        fastcgi_index index.php;
        include fastcgi_params;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        
        # 确保响应头不被缓存
        fastcgi_hide_header Cache-Control;
        fastcgi_hide_header Pragma;
        fastcgi_hide_header Expires;
        
        # 增加超时时间
        fastcgi_read_timeout 300;
        fastcgi_send_timeout 300;
        
        # 禁用 FastCGI 缓存
        fastcgi_ignore_headers Cache-Control Expires Set-Cookie;
        
        # 允许较大的请求和响应
        client_max_body_size 10m;
        fastcgi_buffers 16 16k;
        fastcgi_buffer_size 32k;
    }

    # 其他 PHP 文件的处理
    location ~ [^/]\.php(/|$) {
        try_files $uri =404;
        fastcgi_pass unix:/tmp/php-cgi-80.sock;
        fastcgi_index index.php;
        include fastcgi_params;
        fastcgi_param SCRIPT_FILENAME $document_root$fastcgi_script_name;
        fastcgi_param PATH_INFO $fastcgi_path_info;
    }

    # 静态文件缓存
    location ~* \.(jpg|jpeg|gif|png|css|js|ico|xml)$ {
        expires 30d;
        add_header Cache-Control "public, no-transform";
    }

    # 其他请求处理
    location / {
        try_files $uri $uri/ /index.php?$query_string;
    }

    # 安全限制
    location ~ ^/(\.user.ini|\.htaccess|\.git|\.env|\.svn|\.project|LICENSE|README.md) {
        deny all;
    }

    # 禁止访问隐藏文件
    location ~ /\. {
        deny all;
        access_log off;
        log_not_found off;
    }

    # 日志配置
    access_log /www/wwwlogs/api.geluman.cn.log detailed;
    error_log /www/wwwlogs/api.geluman.cn.error.log;

    # 客户端限制
    client_max_body_size 50m;
    client_body_timeout 300s;
    client_header_timeout 300s;
    keepalive_timeout 300s;
    send_timeout 300s;

    # GZIP 压缩
    gzip on;
    gzip_min_length 1k;
    gzip_comp_level 6;
    gzip_types text/plain text/css text/javascript application/json application/javascript application/x-javascript application/xml;
    gzip_vary on;
    gzip_disable "MSIE [1-6]\.";

    # 基础安全配置
    server_tokens off;
    autoindex off;
}